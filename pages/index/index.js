// pages/index/index.js
import storage from '../../utils/storage.js'
import simpleDataManager from '../../utils/simple-data-manager.js'
import auth from '../../utils/auth.js'
import userManager from '../../utils/userManager.js'
import { PAGES } from '../../utils/constants.js'
import Toast from '../../miniprogram_npm/@vant/weapp/toast/toast'
import unifiedPerformanceManager from '../../utils/unified-performance-manager.js'
import unifiedDataStateManager from '../../utils/unified-data-state-manager.js'
import hapticManager from '../../utils/haptic-manager.js'
// {{ AURA-X: Add - 添加缺失的dataChangeNotifier导入. Approval: 寸止(ID:1738056000). }}
import dataChangeNotifier from '../../utils/data-change-notifier.js'


Page({
  data: {
    appName: '爱巢小记',
    motto: '让记账变得简单而美好',
    userInfo: {},
    hasUserInfo: false,
    hasNotification: true, // 添加通知状态
    isGuest: false,
    // 财务概览数据 - 移除硬编码，使用真实数据
    financialOverview: {
      totalExpense: 0,           // 本月总支出
      dailyExpense: 0,           // 日常支出
      travelExpense: 0,          // 旅行支出
      totalBudget: 0,            // 总预算
      remainingBudget: 0,        // 剩余预算
      usagePercentage: 0,        // 使用百分比
      dailyPercentage: 0,        // 日常支出占比
      travelPercentage: 0        // 旅行支出占比
    },
    // 数据加载状态
    loading: {
      financial: false
    },
    // 页面状态管理
    _lastLoadTime: 0,           // 上次数据加载时间
    _needsRefresh: false,       // 是否需要刷新数据

    // {{ AURA-X: Add - 旅行数据状态. Approval: 寸止(ID:1738056000). }}
    // 旅行数据
    travelData: {
      currentPlan: null,        // 当前计划
      stats: {                  // 统计数据
        totalPlans: 0,
        activePlans: 0,
        completedPlans: 0,
        totalExpenses: 0,
        totalBudget: 0
      },
      recentPlans: [],          // 最近计划
      lastUpdate: 0             // 最后更新时间
    },
    // 旅行数据加载状态
    travelLoading: false
  },

  async onLoad() {
    // 初始化简化数据管理器
    await simpleDataManager.initialize()

    // 登录检查
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    // 使用用户管理器初始化页面
    await userManager.mixinPage(this)

    // {{ AURA-X: Add - 初始化数据版本控制. Approval: 寸止(ID:1738056000). }}
    // 初始化数据版本（首次使用时）
    if (!wx.getStorageSync('financial_data_version')) {
      wx.setStorageSync('financial_data_version', 1)
    }

    // 注册数据变更监听器
    this.setupDataChangeListeners()

    // 加载财务数据（基于数据版本的智能缓存）
    this.loadFinancialDataOptimized()
  },



  async onShow() {
    console.log('首页显示')

    // 🔥 检查登录状态
    const app = getApp()
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/index'
      })
      return
    }

    // 更新用户活动状态（基于新的智能调度器）
    simpleDataManager.updateUserActivity('index')

    // 防抖处理，避免快速切换页面时重复调用（官方建议的性能优化）
    if (this.showTimer) {
      clearTimeout(this.showTimer)
    }

    this.showTimer = setTimeout(() => {
      this.handlePageShow()
    }, 300) // 300ms防抖，基于微信小程序性能优化最佳实践
  },

  // 处理页面显示逻辑
  async handlePageShow() {
    console.log('处理首页显示逻辑')

    // 检查数据版本，如果有变更则立即更新
    const currentVersion = wx.getStorageSync('financial_data_version') || 0
    const lastKnownVersion = this.lastDataVersion || 0

    if (currentVersion > lastKnownVersion) {
      console.log('检测到数据版本变更，立即更新:', currentVersion)
      this.lastDataVersion = currentVersion
      this.loadFinancialDataOptimized(false) // 不强制刷新，使用版本控制
    }

    try {
      const now = Date.now()
      const timeSinceLastLoad = now - this.data._lastLoadTime

      const shouldRefresh = this.data._needsRefresh ||
                           (timeSinceLastLoad > 5 * 60 * 1000 && timeSinceLastLoad > 30 * 1000) ||
                           this.data._lastLoadTime === 0

      if (this.isLoading) {
        return
      }

      if (shouldRefresh) {
        this.isLoading = true

        try {
          // 并行处理用户信息和数据加载
          const loadPromises = []

          // 用户信息处理（减少不必要的刷新）
          if (!this.data.userInfo || !this.data.userInfo.openid || shouldRefresh) {
            loadPromises.push(
              userManager.forceRefreshUserInfo()
                .then(() => userManager.mixinPage(this))
                .then(() => this.refreshUserDisplay())
            )
          }

          // 旅行数据处理
          loadPromises.push(this.loadTravelDataOptimized(shouldRefresh))

          // 并行执行所有加载任务
          await Promise.allSettled(loadPromises)

          // 更新加载时间戳
          unifiedPerformanceManager.smartSetData(this, {
            _lastLoadTime: now,
            _needsRefresh: false
          })

        } finally {
          this.isLoading = false
        }
      }

      // 重置刷新标记
      this.setData({ _needsRefresh: false })

    } catch (error) {
      console.error('首页数据加载失败:', error)
      this.isLoading = false

      // 如果用户信息验证失败，跳转到登录页
      if (error.message && error.message.includes('登录')) {
        wx.reLaunch({
          url: '/pages/login/index'
        })
      }
    }
  },

  // 页面隐藏时的清理逻辑（基于官方建议的性能优化）
  onHide() {
    // 清理防抖定时器，避免内存泄漏
    if (this.showTimer) {
      clearTimeout(this.showTimer)
      this.showTimer = null
    }

    // 重置加载状态标记
    this.isLoading = false

    // 更新用户活动状态
    simpleDataManager.updateUserActivity('')
  },

  // 刷新用户信息显示
  refreshUserDisplay() {
    const userInfo = userManager.getUserInfo()

    // 使用智能setData，只更新变化的数据
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: userInfo,
      hasUserInfo: !!userInfo && !userInfo.isGuest,
      isGuest: userInfo.isGuest || false
    })
  },

  // {{ AURA-X: Modify - 简化页面卸载清理逻辑. Approval: 寸止(ID:1738056000). }}
  // 页面卸载时的清理逻辑
  onUnload() {
    // 清理所有定时器
    if (this.showTimer) {
      clearTimeout(this.showTimer)
      this.showTimer = null
    }

    // 清理用户管理器监听器
    userManager.cleanupPage(this)

    // 清理性能管理器资源
    unifiedPerformanceManager.cleanup()

    // 清理数据变更监听器
    dataChangeNotifier.cleanupPage(this)

    // 重置状态
    this.isLoading = false
  },

  // 设置数据变更监听器（简化版）
  setupDataChangeListeners() {
    // 监听财务数据变更
    simpleDataManager.onChange('financial_overview', (data) => {
      if (data) {
        const processedData = this.processFinancialData(data)
        unifiedPerformanceManager.smartSetData(this, {
          financialOverview: processedData
        })
      }
    })

    // 监听旅行数据变更
    simpleDataManager.onChange('travel_overview', (data) => {
      if (data) {
        const processedData = {
          currentPlan: data.currentPlan || null,
          stats: {
            totalPlans: data.totalPlans || 0,
            activePlans: data.ongoingPlans || 0,
            completedPlans: data.completedPlans || 0,
            totalExpense: data.totalExpense || 0
          }
        }
        unifiedPerformanceManager.smartSetData(this, {
          travelData: processedData
        })
      }
    })
  },

  // 手动刷新数据
  onRefresh() {
    // 更新数据版本，触发财务数据刷新
    const currentVersion = wx.getStorageSync('financial_data_version') || 0
    wx.setStorageSync('financial_data_version', currentVersion + 1)

    // 立即刷新财务数据
    this.loadFinancialDataOptimized(false)

    // 刷新旅行数据
    this.loadTravelDataOptimized(true)

    // 触觉反馈
    hapticManager.impact('light')
  },

  // 处理财务数据，修复百分比显示问题
  processFinancialData(data) {
    const processedData = { ...data }

    // 修复百分比显示问题
    if (processedData.usagePercentage) {
      processedData.usagePercentage = Math.round(processedData.usagePercentage * 100) / 100
    }

    if (processedData.dailyPercentage) {
      processedData.dailyPercentage = Math.round(processedData.dailyPercentage * 100) / 100
    }

    if (processedData.travelPercentage) {
      processedData.travelPercentage = Math.round(processedData.travelPercentage * 100) / 100
    }

    // 计算剩余预算百分比
    if (processedData.totalBudget > 0) {
      const remainingPercentage = 100 - (processedData.usagePercentage || 0)
      processedData.remainingPercentage = Math.max(0, Math.round(remainingPercentage * 100) / 100)
    } else {
      processedData.remainingPercentage = 0
    }

    return processedData
  },



  // 精确的财务数据加载（基于数据版本的智能缓存）
  async loadFinancialDataOptimized(forceRefresh = false) {
    if (!auth.isLoggedIn()) return

    const cacheKey = 'financial_overview_cache'
    const dataVersionKey = 'financial_data_version'

    const currentVersion = wx.getStorageSync(dataVersionKey) || 0
    const cachedVersion = wx.getStorageSync(cacheKey + '_version') || -1
    const cachedData = wx.getStorageSync(cacheKey)

    const needsCloudCall = forceRefresh || !cachedData || currentVersion !== cachedVersion

    if (!needsCloudCall) {
      const processedData = this.processFinancialData(cachedData)
      unifiedPerformanceManager.smartSetData(this, {
        financialOverview: processedData
      })
      return
    }

    if (this.isLoadingFinancial) {
      return
    }
    this.isLoadingFinancial = true

    // 设置加载状态
    unifiedPerformanceManager.smartSetData(this, {
      'loading.financial': true
    })

    try {
      console.log('调用云函数获取财务数据，版本:', currentVersion)

      // 调用云函数获取最新数据
      const result = await wx.cloud.callFunction({
        name: 'expense',
        data: { action: 'getFinancialOverview' }
      })

      if (result.result && result.result.success && result.result.data) {
        const data = result.result.data
        console.log('财务数据加载成功:', data)

        // 缓存数据和版本
        wx.setStorageSync(cacheKey, data)
        wx.setStorageSync(cacheKey + '_version', currentVersion)

        // 处理数据并更新UI
        const processedData = this.processFinancialData(data)
        unifiedPerformanceManager.smartSetData(this, {
          financialOverview: processedData
        })
      } else {
        console.warn('云函数返回数据异常:', result.result)
        this.setDefaultFinancialData()
      }
    } catch (error) {
      console.error('财务数据加载失败:', error)
      this.setDefaultFinancialData()
    } finally {
      this.isLoadingFinancial = false
      unifiedPerformanceManager.smartSetData(this, {
        'loading.financial': false
      })
    }
  },

  // 优化的旅行数据加载（基于增强缓存管理器）
  async loadTravelDataOptimized(forceRefresh = false) {
    if (!auth.isLoggedIn()) return

    try {
      console.log('开始加载旅行数据，强制刷新:', forceRefresh)

      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: true
      })

      // 使用增强的数据管理器，智能缓存和本地计算
      const result = await simpleDataManager.getData(
        'travel_overview',
        async () => {
          // 智能调度云函数调用
          return await simpleDataManager.scheduleCloudCall(
            async () => {
              return await wx.cloud.callFunction({
                name: 'travel',
                data: { action: 'getTravelStatistics' }
              }).then(res => res.result)
            },
            {
              priority: forceRefresh ? 'HIGH' : 'NORMAL',
              canDefer: !forceRefresh,
              timeout: 10000, // 旅行数据可能更复杂，给10秒超时
              context: { page: 'index', action: 'loadTravel' }
            }
          )
        },
        {
          forceRefresh,
          priority: forceRefresh ? 'HIGH' : 'NORMAL',
          ttl: forceRefresh ? 60000 : 600000, // 旅行数据变化较少，可以缓存更久
          enableLocalComputation: true,
          computationFn: (data) => {
            // 使用本地计算引擎处理旅行数据
            return simpleDataManager.calculateLocally(
              'travel_stats',
              null,
              [data] // 传入原始数据给本地计算引擎
            )
          }
        }
      )

      if (result.success && result.data) {
        console.log(`旅行数据加载成功，数据来源: ${result.source || 'unknown'}`)

        // 更新旅行数据
        unifiedPerformanceManager.smartSetData(this, {
          travelData: result.data
        })
      } else {
        console.warn('旅行数据加载失败，使用默认数据')
        // 设置默认旅行数据
        unifiedPerformanceManager.smartSetData(this, {
          travelData: {
            currentPlan: null,
            stats: {
              totalPlans: 0,
              activePlans: 0,
              completedPlans: 0,
              totalExpense: 0
            }
          }
        })
      }
    } catch (error) {
      console.error('旅行数据加载失败:', error)
      // 设置默认旅行数据
      unifiedPerformanceManager.smartSetData(this, {
        travelData: {
          currentPlan: null,
          stats: {
            totalPlans: 0,
            activePlans: 0,
            completedPlans: 0,
            totalExpense: 0
          }
        }
      })
    } finally {
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: false
      })
    }
  },

  // {{ AURA-X: Modify - 使用统一数据服务优化旅行数据加载. Approval: 寸止(ID:1738056000). }}
  /**
   * 加载旅行数据 - 本地优先策略
   * @param {boolean} forceRefresh 强制刷新
   */
  async loadTravelData(forceRefresh = false) {
    try {
      // 设置加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: true
      })

      // 使用简化数据管理器获取旅行数据
      const result = await simpleDataManager.getData(
        'travel_overview',
        async () => {
          // 云函数调用作为备选
          return await wx.cloud.callFunction({
            name: 'travel',
            data: { action: 'getTravelStatistics' }
          }).then(res => res.result)
        },
        { forceRefresh }
      )

      // 处理数据
      const stats = result.success && result.data ? result.data : this.getDefaultStats()
      const plans = result.success && result.data && result.data.recentPlans ? result.data.recentPlans : []

      // 获取当前计划
      const currentPlan = plans.find(plan => {
        const today = new Date()
        const startDate = new Date(plan.startDate)
        const endDate = new Date(plan.endDate)
        return today >= startDate && today <= endDate
      }) || null

      // 组装旅行数据
      const travelData = {
        stats,
        recentPlans: plans.slice(0, 3),
        currentPlan,
        lastUpdate: Date.now()
      }

      // 更新旅行数据
      unifiedPerformanceManager.smartSetData(this, {
        travelData
      })

      // 页面数据已更新

    } catch (error) {
      console.error('加载旅行数据失败:', error)
      this.setDefaultTravelData()
    } finally {
      // 清除加载状态
      unifiedPerformanceManager.smartSetData(this, {
        travelLoading: false
      })
    }
  },

  /**
   * 设置旅行数据实时同步（简化版）
   */
  setupTravelDataSync() {
    // 使用简化数据管理器的变更监听
    simpleDataManager.onChange('travel_overview', (data) => {
      if (data) {
        this.onTravelDataUpdate(data)
      }
    })
  },



  /**
   * 旅行数据更新回调
   * @param {Object} data 旅行数据
   */
  onTravelDataUpdate(data) {
    const processedData = {
      currentPlan: data.currentPlan || null,
      stats: {
        totalPlans: data.totalPlans || 0,
        activePlans: data.ongoingPlans || 0,
        completedPlans: data.completedPlans || 0,
        totalExpense: data.totalExpense || 0
      },
      recentPlans: data.recentPlans ? data.recentPlans.slice(0, 3) : [],
      lastUpdate: Date.now()
    }

    unifiedPerformanceManager.smartSetData(this, {
      travelData: processedData
    })
  },

  /**
   * 设置默认旅行数据
   */
  setDefaultTravelData() {
    unifiedPerformanceManager.smartSetData(this, {
      travelData: {
        currentPlan: null,
        stats: {
          totalPlans: 0,
          completedPlans: 0,
          ongoingPlans: 0,
          plannedPlans: 0,
          totalExpense: 0,
          thisMonthExpense: 0,
          avgExpensePerTrip: 0,
          popularDestinations: []
        },
        recentPlans: [],
        lastUpdate: Date.now()
      }
    })
  },

  /**
   * 查看当前旅行计划
   */
  viewCurrentPlan() {
    const currentPlan = this.data.travelData.currentPlan
    if (!currentPlan) {
      Toast.fail('暂无进行中的旅行计划')
      return
    }

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${currentPlan._id || currentPlan.id}`
    })
  },

  /**
   * 查看旅行计划详情
   * @param {Event} e 事件对象
   */
  viewTravelPlan(e) {
    const planId = e.currentTarget.dataset.planId
    if (!planId) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${planId}`
    })
  },

  /**
   * 前往旅行规划页面
   */
  goToTravelPlanning() {
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  /**
   * 刷新旅行数据
   */
  async refreshTravelData() {
    await this.loadTravelDataOptimized(true)
  },

  // {{ AURA-X: Modify - 修复默认财务数据，确保预算显示正确. Approval: 寸止(ID:1738056000). }}
  // 设置默认财务数据
  setDefaultFinancialData() {
    const defaultData = {
      totalExpense: 0,
      dailyExpense: 0,
      travelExpense: 0,
      budgetUsage: 0,
      remainingBudget: 5000, // 默认预算5000
      totalBudget: 5000, // 默认预算5000
      usagePercentage: 0,
      dailyPercentage: 0,
      travelPercentage: 0,
      remainingPercentage: 100 // 默认剩余100%
    }

    unifiedPerformanceManager.smartSetData(this, {
      financialOverview: defaultData
    })
  },



  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        unifiedPerformanceManager.smartSetData(this, {
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  getUserInfo(e) {
    // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
    unifiedPerformanceManager.smartSetData(this, {
      userInfo: e.detail.userInfo,
      hasUserInfo: true
    })
  },



  // 导航到旅行规划
  navigateToTravelPlanning() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/index'
    })
  },

  // 导航到记账管理 - 默认日常记账
  navigateToAccounting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/account/travel-expense/index?mode=daily'
    })
  },

  // 导航到社交功能
  navigateToSocial() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到发现页面
  navigateToDiscover() {
    hapticManager.navigation()
    wx.switchTab({
      url: '/subpackages/social/discover/index'
    })
  },

  // 导航到预算设置
  navigateToBudgetSetting() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/settings/budget-setting/index'
    })
  },

  // 导航到个人中心
  navigateToProfile() {
    hapticManager.navigation()
    wx.navigateTo({
      url: PAGES.PROFILE
    })
  },

  // 打开调试菜单（长按头像触发）
  openDebugMenu() {
    wx.showActionSheet({
      itemList: ['云函数监控', '清理缓存', '重置统计', '查看状态'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 云函数监控
            wx.navigateTo({
              url: '/pages/debug/cloud-monitor'
            })
            break
          case 1:
            // 清理缓存
            this.clearAllCaches()
            break
          case 2:
            // 重置统计
            this.resetCloudStats()
            break
          case 3:
            // 查看状态
            this.showCloudStatus()
            break
        }
      }
    })
  },

  // 清理所有缓存
  clearAllCaches() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有缓存吗？',
      success: (res) => {
        if (res.confirm) {
          simpleDataManager.clearAllCaches()
          wx.showToast({
            title: '缓存已清理',
            icon: 'success'
          })
        }
      }
    })
  },

  // 重置云函数统计
  resetCloudStats() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置云函数统计数据吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里需要调用云函数管理器的重置方法
          wx.showToast({
            title: '统计已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 显示云函数状态
  showCloudStatus() {
    const status = simpleDataManager.getCacheStatus()
    const message = `缓存状态：
内存使用: ${status.memorySize || 0}
本地计算: ${status.localCalculations || 0}
缓存命中率: ${status.enhancedCache?.cacheHitRate || '0%'}
云函数调用: ${status.enhancedCache?.totalCalls || 0}`

    wx.showModal({
      title: '系统状态',
      content: message,
      showCancel: false
    })
  }
})
